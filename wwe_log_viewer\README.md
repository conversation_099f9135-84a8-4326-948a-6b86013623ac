# WWE Log Viewer

PostgreSQLからログ情報を取得して表示するFlaskベースのWebアプリケーションです。

## 機能

- **認証システム**: PostgreSQLベースのユーザー認証
- **ダッシュボード**: ログ統計の可視化
- **ログ表示**: フィルタリング・検索・ページネーション機能
- **リアルタイム更新**: 自動的な統計データ更新
- **CSVエクスポート**: ログデータのエクスポート機能
- **レスポンシブデザイン**: モバイル対応のモダンUI

## 技術スタック

- **Backend**: Flask, psycopg2, Flask-Session
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Database**: PostgreSQL
- **UI Framework**: Bootstrap 5 + Font Awesome

## セットアップ

### 1. 依存関係のインストール

```bash
pip install -r requirements.txt
```

### 2. PostgreSQLデータベースの準備

PostgreSQLサーバーを起動し、データベースを作成します：

```sql
CREATE DATABASE wwe_logs;
```

### 3. 環境変数の設定

`.env`ファイルを編集して、データベース接続情報を設定します：

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wwe_logs
DB_USER=postgres
DB_PASSWORD=your_password
SECRET_KEY=your-secret-key-change-this-in-production
```

### 4. データベースの初期化

アプリケーションを初回起動すると、自動的にテーブルが作成されます：

```bash
python app.py
```

または、手動でSQLスクリプトを実行：

```bash
psql -U postgres -d wwe_logs -f database/init.sql
```

### 5. ユーザーの作成

デフォルトユーザーを作成するスクリプトを実行：

```bash
python create_user.py
```

## デフォルトログイン情報

- **ユーザー名**: admin
- **パスワード**: admin123

## 使用方法

### アプリケーションの起動

```bash
python app.py
```

ブラウザで `http://localhost:5000` にアクセスします。

### ログデータの追加

PostgreSQLに直接ログデータを挿入するか、アプリケーションのAPIを使用してログを追加できます。

```sql
INSERT INTO logs (timestamp, level, source, message, details) 
VALUES (NOW(), 'INFO', 'APP', 'テストメッセージ', '{"key": "value"}');
```

### フィルタリング

ログ一覧画面では以下の条件でフィルタリングできます：

- 日時範囲
- ログレベル
- ソース名
- メッセージ内容

### CSVエクスポート

ログ一覧画面の「エクスポート」ボタンから、現在のフィルター条件に基づいてCSVファイルをダウンロードできます。

## ディレクトリ構造

```
wwe_log_viewer/
├── app.py                 # メインアプリケーション
├── config.py             # 設定ファイル
├── requirements.txt      # 依存関係
├── create_user.py        # ユーザー作成スクリプト
├── .env                  # 環境変数
├── models/               # データモデル
│   ├── __init__.py
│   ├── database.py       # データベース接続
│   ├── user.py          # ユーザーモデル
│   └── log.py           # ログモデル
├── templates/            # HTMLテンプレート
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   ├── logs.html
│   ├── 404.html
│   └── 500.html
├── static/               # 静的ファイル
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
└── database/
    └── init.sql          # データベース初期化スクリプト
```

## API エンドポイント

- `GET /api/stats` - ログ統計データを取得

## カスタマイズ

### ログレベルの追加

`models/log.py`でログレベルの処理を変更できます。

### UI のカスタマイズ

`static/css/style.css`でスタイルを変更できます。

### 新機能の追加

`app.py`に新しいルートを追加して機能を拡張できます。

## トラブルシューティング

### データベース接続エラー

1. PostgreSQLサーバーが起動していることを確認
2. `.env`ファイルの接続情報が正しいことを確認
3. データベースとユーザーが存在することを確認

### ログイン失敗

1. `create_user.py`を実行してユーザーを作成
2. パスワードが正しいことを確認

### パフォーマンス問題

1. ログテーブルにインデックスが作成されていることを確認
2. 大量データの場合はページネーションサイズを調整

## ライセンス

MIT License

## 開発者

WWE Log Viewer Development Team
