-- WWE Log Viewer データベース初期化スクリプト (SQLite版)

-- ユーザーテーブル
CREATE TABLE IF NOT EXISTS users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- ログテーブル
CREATE TABLE IF NOT EXISTS logs (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP NOT NULL,
    level VARCHAR(20) NOT NULL,
    source VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- インデックス作成
CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);

-- サンプルユーザー作成（パスワード: admin123）
-- パスワードハッシュは実際のWerkzeugで生成されたものを使用
INSERT OR IGNORE INTO users (username, email, password_hash)
VALUES ('admin', '<EMAIL>', 'pbkdf2:sha256:260000$YWRtaW4xMjM$8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918');

-- サンプルログデータ
INSERT OR IGNORE INTO logs (timestamp, level, source, message, details) VALUES
    (datetime('now', '-1 hour'), 'INFO', 'WWE_APP', 'アプリケーション開始', '{"version": "1.0.0"}'),
    (datetime('now', '-50 minutes'), 'DEBUG', 'WWE_DB', 'データベース接続確立', '{"connection_id": "conn_001"}'),
    (datetime('now', '-45 minutes'), 'WARN', 'WWE_AUTH', 'ログイン試行失敗', '{"username": "test_user", "ip": "*************"}'),
    (datetime('now', '-30 minutes'), 'ERROR', 'WWE_API', 'API呼び出しエラー', '{"endpoint": "/api/data", "error": "timeout"}'),
    (datetime('now', '-20 minutes'), 'INFO', 'WWE_SCHEDULER', 'スケジュールタスク実行', '{"task": "cleanup", "duration": "5.2s"}'),
    (datetime('now', '-10 minutes'), 'CRITICAL', 'WWE_SYSTEM', 'システムリソース不足', '{"memory_usage": "95%", "disk_usage": "88%"}'),
    (datetime('now', '-5 minutes'), 'INFO', 'WWE_APP', 'ユーザーログイン', '{"username": "admin", "session_id": "sess_123"}');
