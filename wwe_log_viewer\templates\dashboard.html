{% extends "base.html" %}

{% block title %}ダッシュボード - WWE Log Viewer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>ダッシュボード
        </h1>
    </div>
</div>

<!-- 統計カード -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">総ログ数</h6>
                        <h3 class="mb-0" id="totalLogs">{{ stats.total_logs or 0 }}</h3>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-list"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">警告</h6>
                        <h3 class="mb-0" id="warningLogs">{{ stats.warning_logs or 0 }}</h3>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">エラー</h6>
                        <h3 class="mb-0" id="errorLogs">{{ stats.error_logs or 0 }}</h3>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card stat-card-critical">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">クリティカル</h6>
                        <h3 class="mb-0" id="criticalLogs">{{ stats.critical_logs or 0 }}</h3>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-skull-crossbones"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最新ログ -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>最新ログ
                </h5>
                <a href="{{ url_for('logs') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-list me-1"></i>すべて表示
                </a>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>時刻</th>
                                    <th>レベル</th>
                                    <th>ソース</th>
                                    <th>メッセージ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge log-level-{{ log.level.lower() }}">
                                            {{ log.level }}
                                        </span>
                                    </td>
                                    <td>
                                        <code>{{ log.source }}</code>
                                    </td>
                                    <td>
                                        <span class="log-message" title="{{ log.message }}">
                                            {{ log.message[:100] }}{% if log.message|length > 100 %}...{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">ログデータがありません</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 統計データの自動更新（30秒間隔）
setInterval(function() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalLogs').textContent = data.total_logs || 0;
            document.getElementById('warningLogs').textContent = data.warning_logs || 0;
            document.getElementById('errorLogs').textContent = data.error_logs || 0;
            document.getElementById('criticalLogs').textContent = data.critical_logs || 0;
        })
        .catch(error => console.error('統計データの更新に失敗:', error));
}, 30000);
</script>
{% endblock %}
