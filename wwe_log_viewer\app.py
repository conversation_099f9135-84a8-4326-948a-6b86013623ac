from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_session import Session
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import os
import csv
import io
from config import Config
from models.user import User
from models.log import Log
from models.database import Database

app = Flask(__name__)
app.config.from_object(Config)

# セッション設定
Session(app)

# データベース初期化
def init_database():
    """データベースの初期化"""
    db = Database()
    
    # SQLファイルを読み込んで実行
    try:
        with open('database/init.sql', 'r', encoding='utf-8') as f:
            sql_commands = f.read()
        
        # セミコロンで分割して個別に実行
        commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]

        conn = db.get_connection()
        if conn:
            cursor = conn.cursor()
            for command in commands:
                if command:
                    cursor.execute(command)
            conn.commit()
            conn.close()
            print("データベースの初期化が完了しました")
        else:
            print("データベース接続に失敗しました")
            
    except FileNotFoundError:
        print("database/init.sql ファイルが見つかりません")
    except Exception as e:
        print(f"データベース初期化エラー: {e}")

# ログイン必須デコレータ
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/')
def index():
    """ホームページ"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """ログイン"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('ユーザー名とパスワードを入力してください', 'error')
            return render_template('login.html')
        
        user = User.authenticate(username, password)
        if user:
            session['user_id'] = user.user_id
            session['username'] = user.username
            session['email'] = user.email
            flash(f'ようこそ、{user.username}さん！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('ユーザー名またはパスワードが正しくありません', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """ログアウト"""
    session.clear()
    flash('ログアウトしました', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """ダッシュボード"""
    log_model = Log()
    
    # 統計データを取得
    stats = get_log_statistics()
    
    # 最新ログを取得（10件）
    recent_logs_data = log_model.get_logs(page=1, per_page=10)
    recent_logs = recent_logs_data['logs']
    
    return render_template('dashboard.html', stats=stats, recent_logs=recent_logs)

@app.route('/logs')
@login_required
def logs():
    """ログ一覧"""
    log_model = Log()
    
    # ページネーション
    page = request.args.get('page', 1, type=int)
    per_page = app.config['LOGS_PER_PAGE']
    
    # フィルター条件
    filters = {}
    if request.args.get('start_date'):
        filters['start_date'] = request.args.get('start_date')
    if request.args.get('end_date'):
        filters['end_date'] = request.args.get('end_date')
    if request.args.get('level'):
        filters['level'] = request.args.get('level')
    if request.args.get('source'):
        filters['source'] = request.args.get('source')
    if request.args.get('message'):
        filters['message'] = request.args.get('message')
    
    # CSVエクスポート
    if request.args.get('export') == 'csv':
        return export_logs_csv(filters)
    
    # ログデータを取得
    log_data = log_model.get_logs(page=page, per_page=per_page, filters=filters)
    
    # フィルター用のオプションを取得
    log_levels = log_model.get_log_levels()
    
    return render_template('logs.html', 
                         log_data=log_data, 
                         log_levels=log_levels)

@app.route('/api/stats')
@login_required
def api_stats():
    """統計データAPI"""
    stats = get_log_statistics()
    return jsonify(stats)

def get_log_statistics():
    """ログ統計を取得"""
    db = Database()
    
    # 総ログ数
    total_query = "SELECT COUNT(*) as count FROM logs"
    total_result = db.execute_query_one(total_query)
    total_logs = total_result['count'] if total_result else 0
    
    # レベル別統計
    level_query = """
        SELECT level, COUNT(*) as count 
        FROM logs 
        GROUP BY level
    """
    level_results = db.execute_query(level_query)
    
    stats = {
        'total_logs': total_logs,
        'warning_logs': 0,
        'error_logs': 0,
        'critical_logs': 0
    }
    
    if level_results:
        for result in level_results:
            level = result['level'].upper()
            count = result['count']
            
            if level in ['WARN', 'WARNING']:
                stats['warning_logs'] += count
            elif level == 'ERROR':
                stats['error_logs'] = count
            elif level == 'CRITICAL':
                stats['critical_logs'] = count
    
    return stats

def export_logs_csv(filters=None):
    """ログをCSV形式でエクスポート"""
    log_model = Log()
    
    # 全ログを取得（ページネーションなし）
    log_data = log_model.get_logs(page=1, per_page=10000, filters=filters)
    logs = log_data['logs']
    
    # CSV作成
    output = io.StringIO()
    writer = csv.writer(output)
    
    # ヘッダー
    writer.writerow(['時刻', 'レベル', 'ソース', 'メッセージ', '詳細'])
    
    # データ
    for log in logs:
        writer.writerow([
            log['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
            log['level'],
            log['source'],
            log['message'],
            str(log['details']) if log['details'] else ''
        ])
    
    # レスポンス作成
    output.seek(0)
    response = app.response_class(
        output.getvalue(),
        mimetype='text/csv',
        headers={'Content-Disposition': f'attachment; filename=logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
    )
    
    return response

@app.errorhandler(404)
def not_found_error(error):
    """404エラーハンドラ"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500エラーハンドラ"""
    return render_template('500.html'), 500

if __name__ == '__main__':
    # データベース初期化
    init_database()
    
    # アプリケーション起動
    app.run(debug=True, host='0.0.0.0', port=5000)
