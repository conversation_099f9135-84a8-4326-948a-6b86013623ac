// WWE Log Viewer メインJavaScript

document.addEventListener('DOMContentLoaded', function() {
    // ページ読み込み時のアニメーション
    document.body.classList.add('fade-in');
    
    // ツールチップの初期化
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 自動更新の設定
    setupAutoRefresh();
    
    // フィルターフォームの拡張機能
    setupFilterForm();
    
    // ログテーブルの拡張機能
    setupLogTable();
});

// 自動更新機能
function setupAutoRefresh() {
    // ダッシュボードページでのみ自動更新を有効化
    if (window.location.pathname === '/dashboard' || window.location.pathname === '/') {
        setInterval(function() {
            refreshDashboardStats();
        }, 30000); // 30秒間隔
    }
}

// ダッシュボード統計の更新
function refreshDashboardStats() {
    fetch('/api/stats')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            updateStatCard('totalLogs', data.total_logs);
            updateStatCard('warningLogs', data.warning_logs);
            updateStatCard('errorLogs', data.error_logs);
            updateStatCard('criticalLogs', data.critical_logs);
        })
        .catch(error => {
            console.error('統計データの更新に失敗:', error);
        });
}

// 統計カードの更新
function updateStatCard(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (element) {
        const currentValue = parseInt(element.textContent);
        if (currentValue !== newValue) {
            element.style.transform = 'scale(1.1)';
            element.textContent = newValue;
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }
    }
}

// フィルターフォームの設定
function setupFilterForm() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        // リアルタイム検索（デバウンス付き）
        const searchInputs = filterForm.querySelectorAll('input[type="text"]');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        filterForm.submit();
                    }
                }, 1000);
            });
        });
        
        // 日付フィールドの自動設定
        setupDateFields();
    }
}

// 日付フィールドの設定
function setupDateFields() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    if (startDateInput && endDateInput) {
        // 開始日が終了日より後の場合、終了日を自動調整
        startDateInput.addEventListener('change', function() {
            if (endDateInput.value && this.value > endDateInput.value) {
                endDateInput.value = this.value;
            }
        });
        
        // 終了日が開始日より前の場合、開始日を自動調整
        endDateInput.addEventListener('change', function() {
            if (startDateInput.value && this.value < startDateInput.value) {
                startDateInput.value = this.value;
            }
        });
    }
}

// ログテーブルの設定
function setupLogTable() {
    // ログメッセージのクリックで詳細表示
    const logMessages = document.querySelectorAll('.log-message');
    logMessages.forEach(message => {
        message.addEventListener('click', function() {
            const fullMessage = this.getAttribute('title') || this.textContent;
            showMessageModal(fullMessage);
        });
    });
    
    // ログ行のハイライト
    const logRows = document.querySelectorAll('.log-row');
    logRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(13, 110, 253, 0.1)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// フィルターのクリア
function clearFilters() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        filterForm.reset();
        // URLからクエリパラメータを削除
        window.location.href = window.location.pathname;
    }
}

// ログの更新
function refreshLogs() {
    const refreshBtn = document.querySelector('button[onclick="refreshLogs()"]');
    if (refreshBtn) {
        const originalContent = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>更新中...';
        refreshBtn.disabled = true;
        
        // ページをリロード
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }
}

// ログ詳細の表示
function showLogDetails(logId, details) {
    try {
        const detailsObj = typeof details === 'string' ? JSON.parse(details) : details;
        const formattedDetails = JSON.stringify(detailsObj, null, 2);
        
        document.getElementById('logDetailsContent').textContent = formattedDetails;
        
        const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
        modal.show();
    } catch (error) {
        console.error('ログ詳細の表示に失敗:', error);
        alert('ログ詳細の表示に失敗しました');
    }
}

// メッセージモーダルの表示
function showMessageModal(message) {
    // 簡易モーダルを作成
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ログメッセージ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre style="white-space: pre-wrap; word-wrap: break-word;">${message}</pre>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    // モーダルが閉じられたら要素を削除
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// ログのエクスポート
function exportLogs() {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', 'csv');
    
    // ダウンロードリンクを作成
    const link = document.createElement('a');
    link.href = currentUrl.toString();
    link.download = `logs_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// エラーハンドリング
window.addEventListener('error', function(event) {
    console.error('JavaScript エラー:', event.error);
});

// 未処理のPromise拒否をキャッチ
window.addEventListener('unhandledrejection', function(event) {
    console.error('未処理のPromise拒否:', event.reason);
});

// ユーティリティ関数
const Utils = {
    // 日付フォーマット
    formatDate: function(date) {
        return new Date(date).toLocaleString('ja-JP');
    },
    
    // ログレベルの色を取得
    getLogLevelColor: function(level) {
        const colors = {
            'DEBUG': '#6c757d',
            'INFO': '#0dcaf0',
            'WARN': '#ffc107',
            'WARNING': '#ffc107',
            'ERROR': '#dc3545',
            'CRITICAL': '#6f42c1'
        };
        return colors[level.toUpperCase()] || '#6c757d';
    },
    
    // 文字列の切り詰め
    truncateString: function(str, maxLength) {
        if (str.length <= maxLength) return str;
        return str.substring(0, maxLength) + '...';
    }
};
