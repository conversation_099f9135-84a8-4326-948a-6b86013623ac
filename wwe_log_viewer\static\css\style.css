/* WWE Log Viewer カスタムスタイル */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

/* ログイン画面 */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.login-header h2 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.login-form .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.login-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.login-form .btn {
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* 統計カード */
.stat-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-card-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
    color: white;
}

.stat-card-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    color: white;
}

.stat-card-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
    color: white;
}

.stat-card-critical {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* ログレベルバッジ */
.log-level-info {
    background-color: var(--info-color);
    color: white;
}

.log-level-debug {
    background-color: var(--secondary-color);
    color: white;
}

.log-level-warn,
.log-level-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.log-level-error {
    background-color: var(--danger-color);
    color: white;
}

.log-level-critical {
    background-color: #6f42c1;
    color: white;
}

/* ログテーブル */
.log-row {
    transition: all 0.2s ease;
}

.log-row:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.log-message-container {
    max-width: 400px;
}

.log-message {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.log-message:hover {
    white-space: normal;
    overflow: visible;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 5px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

/* カード */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* ボタン */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ナビゲーション */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* フォーム */
.form-control,
.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* ページネーション */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* モーダル */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

/* アラート */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* レスポンシブ */
@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .log-message-container {
        max-width: 200px;
    }
}

/* アニメーション */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* スクロールバー */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
