import sqlite3
from config import Config

class Database:
    def __init__(self):
        self.config = Config()
        self.db_path = getattr(self.config, 'DB_PATH', 'wwe_logs.db')

    def get_connection(self):
        """SQLiteデータベースへの接続を取得"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 辞書形式でアクセス可能
            return conn
        except sqlite3.Error as e:
            print(f"データベース接続エラー: {e}")
            return None
    
    def execute_query(self, query, params=None):
        """クエリを実行して結果を返す"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                if query.strip().upper().startswith('SELECT'):
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return cursor.rowcount
        except sqlite3.Error as e:
            print(f"クエリ実行エラー: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()
    
    def execute_query_one(self, query, params=None):
        """クエリを実行して1行だけ返す"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchone()
        except sqlite3.Error as e:
            print(f"クエリ実行エラー: {e}")
            return None
        finally:
            conn.close()
