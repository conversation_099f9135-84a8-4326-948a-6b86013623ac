from models.database import Database
from datetime import datetime

class Log:
    def __init__(self):
        self.db = Database()
    
    def get_logs(self, page=1, per_page=50, filters=None):
        """ログを取得（ページネーション付き）"""
        offset = (page - 1) * per_page
        
        # ベースクエリ
        base_query = """
            SELECT log_id, timestamp, level, source, message, details
            FROM logs
        """
        
        count_query = "SELECT COUNT(*) as total FROM logs"
        
        # フィルター条件を構築
        where_conditions = []
        params = []
        
        if filters:
            if filters.get('start_date'):
                where_conditions.append("timestamp >= %s")
                params.append(filters['start_date'])
            
            if filters.get('end_date'):
                where_conditions.append("timestamp <= %s")
                params.append(filters['end_date'])
            
            if filters.get('level'):
                where_conditions.append("level = %s")
                params.append(filters['level'])
            
            if filters.get('source'):
                where_conditions.append("source ILIKE %s")
                params.append(f"%{filters['source']}%")
            
            if filters.get('message'):
                where_conditions.append("message ILIKE %s")
                params.append(f"%{filters['message']}%")
        
        # WHERE句を追加
        if where_conditions:
            where_clause = " WHERE " + " AND ".join(where_conditions)
            base_query += where_clause
            count_query += where_clause
        
        # ORDER BY と LIMIT を追加
        base_query += " ORDER BY timestamp DESC LIMIT %s OFFSET %s"
        params.extend([per_page, offset])
        
        # ログデータを取得
        logs = self.db.execute_query(base_query, params)
        
        # 総件数を取得
        count_params = params[:-2] if where_conditions else []
        total_result = self.db.execute_query_one(count_query, count_params)
        total = total_result['total'] if total_result else 0
        
        return {
            'logs': logs or [],
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page
        }
    
    def get_log_levels(self):
        """利用可能なログレベルを取得"""
        query = "SELECT DISTINCT level FROM logs ORDER BY level"
        result = self.db.execute_query(query)
        return [row['level'] for row in result] if result else []
    
    def get_log_sources(self):
        """利用可能なログソースを取得"""
        query = "SELECT DISTINCT source FROM logs ORDER BY source"
        result = self.db.execute_query(query)
        return [row['source'] for row in result] if result else []
