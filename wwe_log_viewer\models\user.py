from werkzeug.security import check_password_hash
from models.database import Database

class User:
    def __init__(self, user_id=None, username=None, email=None, password_hash=None):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.db = Database()
    
    @classmethod
    def authenticate(cls, username, password):
        """ユーザー認証"""
        db = Database()
        query = """
            SELECT user_id, username, email, password_hash 
            FROM users 
            WHERE username = %s OR email = %s
        """
        user_data = db.execute_query_one(query, (username, username))
        
        if user_data and check_password_hash(user_data['password_hash'], password):
            return cls(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                password_hash=user_data['password_hash']
            )
        return None
    
    @classmethod
    def get_by_id(cls, user_id):
        """IDでユーザーを取得"""
        db = Database()
        query = """
            SELECT user_id, username, email, password_hash 
            FROM users 
            WHERE user_id = %s
        """
        user_data = db.execute_query_one(query, (user_id,))
        
        if user_data:
            return cls(
                user_id=user_data['user_id'],
                username=user_data['username'],
                email=user_data['email'],
                password_hash=user_data['password_hash']
            )
        return None
    
    def to_dict(self):
        """ユーザー情報を辞書形式で返す（パスワードハッシュは除く）"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email
        }
