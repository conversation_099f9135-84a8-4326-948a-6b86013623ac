#!/usr/bin/env python3
"""
ユーザー作成スクリプト
パスワードハッシュを生成してデータベースに挿入します
"""

from werkzeug.security import generate_password_hash
from models.database import Database

def create_user(username, email, password):
    """新しいユーザーを作成"""
    db = Database()
    
    # パスワードハッシュを生成
    password_hash = generate_password_hash(password)
    
    # ユーザーを挿入
    query = """
        INSERT INTO users (username, email, password_hash) 
        VALUES (%s, %s, %s)
        ON CONFLICT (username) DO UPDATE SET
        email = EXCLUDED.email,
        password_hash = EXCLUDED.password_hash
    """
    
    result = db.execute_query(query, (username, email, password_hash))
    
    if result is not None:
        print(f"ユーザー '{username}' を作成/更新しました")
        print(f"パスワードハッシュ: {password_hash}")
        return True
    else:
        print(f"ユーザー '{username}' の作成に失敗しました")
        return False

if __name__ == '__main__':
    # デフォルトの管理者ユーザーを作成
    create_user('admin', '<EMAIL>', 'admin123')
    
    # 追加のテストユーザーを作成
    create_user('user1', '<EMAIL>', 'password123')
    create_user('viewer', '<EMAIL>', 'viewer123')
