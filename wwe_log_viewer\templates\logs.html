{% extends "base.html" %}

{% block title %}ログ一覧 - WWE Log Viewer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-list me-2"></i>ログ一覧
        </h1>
    </div>
</div>

<!-- フィルター -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>フィルター
            <button class="btn btn-sm btn-outline-secondary ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                <i class="fas fa-chevron-down"></i>
            </button>
        </h5>
    </div>
    <div class="collapse show" id="filterCollapse">
        <div class="card-body">
            <form method="GET" id="filterForm">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label">開始日時</label>
                        <input type="datetime-local" class="form-control" id="start_date" name="start_date" 
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label">終了日時</label>
                        <input type="datetime-local" class="form-control" id="end_date" name="end_date"
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="level" class="form-label">レベル</label>
                        <select class="form-select" id="level" name="level">
                            <option value="">すべて</option>
                            {% for level in log_levels %}
                            <option value="{{ level }}" {% if request.args.get('level') == level %}selected{% endif %}>
                                {{ level }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="source" class="form-label">ソース</label>
                        <input type="text" class="form-control" id="source" name="source" 
                               placeholder="ソース名" value="{{ request.args.get('source', '') }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="message" class="form-label">メッセージ</label>
                        <input type="text" class="form-control" id="message" name="message" 
                               placeholder="検索キーワード" value="{{ request.args.get('message', '') }}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>検索
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>クリア
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt me-1"></i>更新
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- ログテーブル -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            ログデータ ({{ log_data.total }}件中 {{ ((log_data.page - 1) * log_data.per_page + 1) }}-{{ 
                [log_data.page * log_data.per_page, log_data.total] | min }}件を表示)
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportLogs()">
                <i class="fas fa-download me-1"></i>エクスポート
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        {% if log_data.logs %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 180px;">時刻</th>
                            <th style="width: 100px;">レベル</th>
                            <th style="width: 150px;">ソース</th>
                            <th>メッセージ</th>
                            <th style="width: 80px;">詳細</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in log_data.logs %}
                        <tr class="log-row" data-log-id="{{ log.log_id }}">
                            <td>
                                <small class="text-muted">
                                    {{ log.timestamp.strftime('%Y-%m-%d') }}<br>
                                    {{ log.timestamp.strftime('%H:%M:%S') }}
                                </small>
                            </td>
                            <td>
                                <span class="badge log-level-{{ log.level.lower() }}">
                                    {{ log.level }}
                                </span>
                            </td>
                            <td>
                                <code class="small">{{ log.source }}</code>
                            </td>
                            <td>
                                <div class="log-message-container">
                                    <span class="log-message">{{ log.message }}</span>
                                </div>
                            </td>
                            <td>
                                {% if log.details %}
                                <button class="btn btn-outline-info btn-sm" onclick="showLogDetails({{ log.log_id }}, '{{ log.details | tojson | safe }}')">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">ログが見つかりません</h5>
                <p class="text-muted">フィルター条件を変更して再度検索してください</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- ページネーション -->
{% if log_data.total_pages > 1 %}
<nav aria-label="ログページネーション" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if log_data.page > 1 %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('logs', page=log_data.page-1, **request.args) }}">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
        {% endif %}
        
        {% for page_num in range(1, log_data.total_pages + 1) %}
            {% if page_num == log_data.page %}
            <li class="page-item active">
                <span class="page-link">{{ page_num }}</span>
            </li>
            {% elif page_num <= 3 or page_num > log_data.total_pages - 3 or (page_num >= log_data.page - 2 and page_num <= log_data.page + 2) %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('logs', page=page_num, **request.args) }}">{{ page_num }}</a>
            </li>
            {% elif page_num == 4 or page_num == log_data.total_pages - 3 %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if log_data.page < log_data.total_pages %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('logs', page=log_data.page+1, **request.args) }}">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- ログ詳細モーダル -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ログ詳細</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="logDetailsContent"></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}
